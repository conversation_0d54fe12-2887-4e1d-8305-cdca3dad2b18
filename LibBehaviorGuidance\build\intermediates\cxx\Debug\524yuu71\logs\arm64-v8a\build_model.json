{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\.cxx\\Debug\\524yuu71\\arm64-v8a", "soFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cxx\\Debug\\524yuu71\\obj\\arm64-v8a", "soRepublishFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 29, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\.cxx", "intermediatesBaseFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates", "intermediatesFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cxx", "gradleModulePathName": ":LibBehaviorGuidance", "moduleRootFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance", "moduleBuildFile": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build.gradle.kts", "makeFile": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\mit_dd_gazetracker", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\.cxx\\Debug\\524yuu71\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "524yuu71676u6u3u585dvb4p311n1f56254z4h3z626g251x4r6y1e156d", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.3.1.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/LibBehaviorGuidance/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=29\n-DANDROID_PLATFORM=android-29\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DC<PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/LibBehaviorGuidance/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/LibBehaviorGuidance/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/LibBehaviorGuidance/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HD:\\mit_dd_gazetracker\\LibBehaviorGuidance\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=29", "-DANDROID_PLATFORM=android-29", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cxx\\Debug\\524yuu71\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cxx\\Debug\\524yuu71\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\mit_dd_gazetracker\\LibBehaviorGuidance\\.cxx\\Debug\\524yuu71\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\build\\intermediates\\cxx\\Debug\\524yuu71"}