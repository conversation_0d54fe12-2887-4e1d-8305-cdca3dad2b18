[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: arm64-v8a", "file_": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\.cxx\\Debug\\524yuu71\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\mit_dd_gazetracker\\LibBehaviorGuidance\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]