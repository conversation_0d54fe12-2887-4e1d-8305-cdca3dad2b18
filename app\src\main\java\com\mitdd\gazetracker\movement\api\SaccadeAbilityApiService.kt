package com.mitdd.gazetracker.movement.api

import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

/**
 * FileName: SaccadeAbilityApiService
 * Author by lilin,Date on 2025/6/21 10:30
 * PS: Not easy to write code, please indicate.
 * 扫视能力检测API服务
 */
interface SaccadeAbilityApiService {

    /**
     * 提交扫视能力检测结果
     */
    @POST("api/movement/saccade-ability/submit")
    suspend fun addSaccadeAbility(
        @Body saccadeAbilityReq: RequestBody
    ): ApiResponse<SaccadeAbilityAdd>

    /**
     * 上传图片
     */
    @Multipart
    @POST("api/files/upload/image")
    suspend fun uploadImage(
        @Part file: MultipartBody.Part
    ): ApiResponse<FileUploadResponse>
}
