package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.mitdd.gazetracker.movement.api.EMPatientApiService
import com.mitdd.gazetracker.movement.bean.EMPatient
import com.mitdd.gazetracker.movement.bean.EMPatientAdd
import com.mitdd.gazetracker.movement.bean.EMPatientList
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.net.UrlConfig
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: EMPatientRepository
 * Author by lilin,Date on 2025/5/14 10:00
 * PS: Not easy to write code, please indicate.
 * 眼球运动评估患者Repository
 */
class EMPatientRepository : BaseRepository() {

    companion object {
        private const val TAG = "EMPatientRepository"
    }

    /**
     * 添加眼球运动评估患者
     * @param emPatient 眼球运动评估患者信息
     */
    suspend fun addEMPatient(emPatient: EMPatient): ApiResponse<EMPatientAdd> {
        Logger.d(TAG, msg = "Repository开始添加眼球运动评估患者")
        Logger.d(TAG, msg = "患者信息 - 姓名: ${emPatient.name}, 性别: ${emPatient.gender}, 年龄: ${emPatient.age}")

        return executeHttp {
            val hashMap = HashMap<String, Any>()
            emPatient.name?.let { hashMap["name"] = it }
            emPatient.inpatientNum?.let { hashMap["inpatientNum"] = it }
            emPatient.caseCardNum?.let { hashMap["caseCardNum"] = it }
            emPatient.birthday?.let { hashMap["birthday"] = it }
            emPatient.age?.let { hashMap["age"] = it }
            emPatient.gender?.let { hashMap["gender"] = it }
            emPatient.patientType?.let { hashMap["patientType"] = it }
            emPatient.phone?.let { hashMap["phone"] = it }
            emPatient.diagnosisInformation?.let { hashMap["diagnosisInformation"] = it }

            val json = gson.toJson(hashMap)
            Logger.d(TAG, msg = "添加患者请求JSON: $json")
            val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())

            Logger.d(TAG, msg = "发送添加患者HTTP请求")
            val response = MainRetrofitClient.createService(EMPatientApiService::class.java, UrlConfig.MOVEMENT_DOMAIN).addEMPatient(requestBody)
            Logger.d(TAG, msg = "添加患者HTTP请求完成")
            response
        }
    }

    /**
     * 获取眼球运动评估患者列表
     * @param page 页码,从1开始
     * @param size 每页条数,示例值(10)
     * @param sort 排序条件,示例值(按创建时间倒序：createTime,desc)
     * @param gender 性别{1=男, 2=女},可用值:0,1,2,示例值(1)
     * @param keywords 搜索关键词,示例值(张三)
     */
    suspend fun getEMPatientList(
        page: Int,
        size: Int,
        sort: String?,
        gender: Int?,
        keywords: String?
    ): ApiResponse<EMPatientList> {
        Logger.d(TAG, msg = "Repository获取眼球运动评估患者列表")
        Logger.d(TAG, msg = "请求参数 - page: $page, size: $size, sort: $sort, gender: $gender, keywords: $keywords")

        return executeHttp {
            Logger.d(TAG, msg = "发送获取患者列表HTTP请求")
            val response = MainRetrofitClient.createService(EMPatientApiService::class.java, UrlConfig.MOVEMENT_DOMAIN).getEMPatientList(
                page, size, sort, gender, keywords
            )
            Logger.d(TAG, msg = "获取患者列表HTTP请求完成")
            response
        }
    }

    /**
     * 查询眼球运动评估患者信息
     * @param id 患者ID,示例值(1)
     */
    suspend fun queryEMPatient(id: String): ApiResponse<EMPatient> {
        Logger.d(TAG, msg = "Repository查询眼球运动评估患者信息 - 患者ID: $id")

        return executeHttp {
            Logger.d(TAG, msg = "发送查询患者信息HTTP请求 - 患者ID: $id")
            val response = MainRetrofitClient.createService(EMPatientApiService::class.java, UrlConfig.MOVEMENT_DOMAIN).queryEMPatient(id)
            Logger.d(TAG, msg = "查询患者信息HTTP请求完成 - 患者ID: $id")
            response
        }
    }

    /**
     * 修改眼球运动评估患者
     * @param id 患者ID,示例值(1)
     * @param emPatient 眼球运动评估患者信息
     */
    suspend fun modifyEMPatient(id: String, emPatient: EMPatient): ApiResponse<Any> {
        Logger.d(TAG, msg = "Repository修改眼球运动评估患者 - 患者ID: $id")
        Logger.d(TAG, msg = "修改患者信息 - 姓名: ${emPatient.name}, 性别: ${emPatient.gender}, 年龄: ${emPatient.age}")

        return executeHttp {
            val hashMap = HashMap<String, Any>()
            emPatient.name?.let { hashMap["name"] = it }
            emPatient.inpatientNum?.let { hashMap["inpatientNum"] = it }
            emPatient.caseCardNum?.let { hashMap["caseCardNum"] = it }
            emPatient.birthday?.let { hashMap["birthday"] = it }
            emPatient.age?.let { hashMap["age"] = it }
            emPatient.gender?.let { hashMap["gender"] = it }
            emPatient.patientType?.let { hashMap["patientType"] = it }
            emPatient.phone?.let { hashMap["phone"] = it }
            emPatient.diagnosisInformation?.let { hashMap["diagnosisInformation"] = it }

            val json = gson.toJson(hashMap)
            Logger.d(TAG, msg = "修改患者请求JSON: $json")
            val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())

            Logger.d(TAG, msg = "发送修改患者HTTP请求 - 患者ID: $id")
            val response = MainRetrofitClient.createService(EMPatientApiService::class.java, UrlConfig.MOVEMENT_DOMAIN).modifyEMPatient(id, requestBody)
            Logger.d(TAG, msg = "修改患者HTTP请求完成 - 患者ID: $id")
            response
        }
    }

    /**
     * 删除眼球运动评估患者
     * @param id 患者ID,示例值(1)
     */
    suspend fun deleteEMPatient(id: String): ApiResponse<Any> {
        Logger.d(TAG, msg = "Repository删除眼球运动评估患者 - 患者ID: $id")

        return executeHttp {
            Logger.d(TAG, msg = "发送删除患者HTTP请求 - 患者ID: $id")
            val response = MainRetrofitClient.createService(EMPatientApiService::class.java, UrlConfig.MOVEMENT_DOMAIN).deleteEMPatient(id)
            Logger.d(TAG, msg = "删除患者HTTP请求完成 - 患者ID: $id")
            response
        }
    }
}