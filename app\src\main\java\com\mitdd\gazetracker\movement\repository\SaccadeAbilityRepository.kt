package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.api.SaccadeAbilityApiService
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.net.UrlConfig
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * FileName: SaccadeAbilityRepository
 * Author by lilin,Date on 2025/6/21 10:30
 * PS: Not easy to write code, please indicate.
 * 扫视能力检测Repository
 */
class SaccadeAbilityRepository : BaseRepository() {

    companion object {
        private val TAG = SaccadeAbilityRepository::class.java.simpleName
    }

    /**
     * 上传图片
     * @param file 图片文件
     */
    suspend fun uploadImage(file: MultipartBody.Part): ApiResponse<FileUploadResponse> {
        Logger.d(TAG, msg = "开始上传扫视能力检测结果图片")
        return executeHttp {
            val response = MainRetrofitClient.createService(SaccadeAbilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .uploadImage(file)
            Logger.d(TAG, msg = "扫视能力检测结果图片上传完成")
            response
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param params 提交参数
     */
    suspend fun submitSaccadeAbilityResult(params: HashMap<String, Any>): ApiResponse<SaccadeAbilityAdd> {
        Logger.d(TAG, msg = "开始提交扫视能力检测结果")
        Logger.d(TAG, msg = "提交参数: ${gson.toJson(params)}")
        
        return executeHttp {
            val json = gson.toJson(params)
            val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            
            Logger.d(TAG, msg = "发送扫视能力检测结果HTTP请求")
            val response = MainRetrofitClient.createService(SaccadeAbilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .addSaccadeAbility(requestBody)
            Logger.d(TAG, msg = "扫视能力检测结果HTTP请求完成")
            response
        }
    }
}
