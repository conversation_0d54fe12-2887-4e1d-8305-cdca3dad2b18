package com.mitdd.gazetracker.movement.saccade

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.EyeMovementResultActivity
import com.mitdd.gazetracker.movement.vm.EMPatientViewModel
import com.mitdd.gazetracker.movement.vm.SaccadeAbilityViewModel
import java.io.File
import java.io.FileOutputStream

/**
 * FileName: SaccadeAbilityEvaluateResultActivity
 * Author by lilin,Date on 2024/12/11 14:52
 * PS: Not easy to write code, please indicate.
 * 追随能力评估结果
 */
class SaccadeAbilityEvaluateResultActivity : EyeMovementResultActivity() {

    companion object{
        private val TAG = SaccadeAbilityEvaluateResultActivity::class.java.simpleName

        const val INPUT_PARAM_EVALUATE_RESULT = "evaluate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, SaccadeAbilityEvaluateResultActivity::class.java)
        }
    }

    private val evaluateResultView by id<SaccadeAbilityEvaluateResultView>(R.id.evaluate_result_view)
    private val saccadeAbilityViewModel: SaccadeAbilityViewModel by viewModels()
    private val patientViewModel: EMPatientViewModel by viewModels()

    // 存储视线轨迹数据和目标点数据
    private var gazePoints: List<GazePoint> = emptyList()
    private var targetPoints: List<Pair<Float, Float>> = emptyList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_saccade_ability_evaluate_result)

        initView()
        initObserver()
        initData()
        initViewModelObserver()
    }

    private fun initView(){
        getTitleView().text = getString(R.string.str_saccade_ability_evaluate_result)
        getEvaluateResultView().apply {
            text = getString(R.string.str_instability)
            setBackgroundResource(R.drawable.common_eb4e89_round_5_bg)
        }
        getPointNumberView().isVisible = true
        getAverageDurationView().isVisible = true
    }

    private fun initObserver(){
        LiveEventBus.get<List<GazePoint>>(INPUT_PARAM_EVALUATE_RESULT).observeSticky(this){ points ->
            if (!points.isNullOrEmpty()){
                Logger.d(TAG, msg = "接收到扫视能力评估结果数据，视线点数量: ${points.size}")

                // 打印视线点数据详情
                points.forEachIndexed { index, point ->
                    Logger.d(TAG, msg = "视线点[$index]: x=${point.x}, y=${point.y}, duration=${point.duration}ms")
                }

                gazePoints = points
                evaluateResultView.drawResult(points)

                // 等待视图绘制完成后获取截图并上传
                evaluateResultView.post {
                    saveAndUploadResultImage()
                }
            }
        }
    }

    private fun initData() {
        Logger.d(TAG, msg = "初始化扫视能力评估结果页面数据")
    }

    private fun initViewModelObserver() {
        // 观察图片上传结果
        saccadeAbilityViewModel.uploadImageResultLiveData.observe(this, Observer { result ->
            if (result != null) {
                Logger.d(TAG, msg = "图片上传成功，URL: ${result.url}")
                // 图片上传成功后提交检测结果
                submitSaccadeAbilityResult(result.url)
            } else {
                Logger.e(TAG, msg = "图片上传失败")
                // 即使图片上传失败，也可以提交检测结果（不包含图片URL）
                submitSaccadeAbilityResult(null)
            }
        })

        // 观察检测结果提交结果
        saccadeAbilityViewModel.submitResultLiveData.observe(this, Observer { result ->
            if (result != null) {
                Logger.d(TAG, msg = "扫视能力检测结果提交成功: ${result.message}")
            } else {
                Logger.e(TAG, msg = "扫视能力检测结果提交失败")
            }
        })
    }

    override fun getSerialNumberType():String {
        return "03"
    }




    /**
     * 保存结果图片并上传
     */
    private fun saveAndUploadResultImage() {
        try {
            Logger.d(TAG, msg = "开始保存扫视能力评估结果图片")

            // 获取视图的bitmap
            val bitmap = evaluateResultView.getViewBitmap()
            if (bitmap != null) {
                // 保存到文件
                val file = File(cacheDir, "saccade_ability_result_${System.currentTimeMillis()}.png")
                val fos = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                fos.close()

                Logger.d(TAG, msg = "结果图片保存成功: ${file.absolutePath}")
                Logger.d(TAG, msg = "图片大小: ${file.length()} bytes")

                // 上传图片
                saccadeAbilityViewModel.uploadImage(file)
            } else {
                Logger.e(TAG, msg = "无法获取结果视图的bitmap")
                // 直接提交结果（不包含图片）
                submitSaccadeAbilityResult(null)
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "保存结果图片失败: ${e.message}")
            // 直接提交结果（不包含图片）
            submitSaccadeAbilityResult(null)
        }
    }

    /**
     * 提交扫视能力检测结果
     */
    private fun submitSaccadeAbilityResult(imageUrl: String?) {
        val patientId = patientViewModel.currentPatientLiveData.value?.id?.toLongOrNull()
        if (patientId == null) {
            Logger.e(TAG, msg = "患者ID为空，无法提交检测结果")
            return
        }

        Logger.d(TAG, msg = "开始提交扫视能力检测数据")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点数量: ${targetPoints.size}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "图片URL: $imageUrl")

        // 构建扫视能力特有数据
        val saccadeAbilityData = HashMap<String, Any>()
        // 这里可以添加更多扫视能力特有的分析数据

        saccadeAbilityViewModel.submitSaccadeAbilityResult(
            patientId = patientId,
            targetPoints = targetPoints,
            gazePoints = gazePoints,
            duration = 20000,
            notes = "扫视能力测试",
            imageUrl = imageUrl,
            saccadeAbilityData = saccadeAbilityData
        )
    }



}