package com.mitdd.gazetracker.movement.saccade

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.gaze.bean.GazePoint

/**
 * FileName: SaccadeAbilityEvaluateResultView
 * Author by lilin,Date on 2024/12/11 14:58
 * PS: Not easy to write code, please indicate.
 */
class SaccadeAbilityEvaluateResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr){

    companion object {
        private val TAG = SaccadeAbilityEvaluateResultView::class.java.simpleName
    }

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)
    //视点半径
    private var gazePointRadius = 10.dp2px(context)
    //视点列表
    private val gazePoints = mutableListOf<GazePoint>()
    //视点路径
    private val gazePath = Path()
    //视点画笔
    private val gazePointPaint = Paint().apply {
        color = Color.parseColor("#F28225")
        style = Paint.Style.FILL
        //抗锯齿
        isAntiAlias = true
    }
    //视点序号画笔
    private val gazeIndexPaint = Paint().apply {
        color = Color.parseColor("#333333")
        style = Paint.Style.FILL
        isAntiAlias = true
        textSize = 10f
        textAlign = Paint.Align.CENTER
    }
    //视点时间画笔
    private val gazeDurationPaint = Paint().apply {
        color = Color.parseColor("#F28225")
        style = Paint.Style.FILL
        isAntiAlias = true
        textSize = 10f
        textAlign = Paint.Align.CENTER
    }
    //视点路径画笔
    private val gazePathPaint = Paint().apply {
        color = Color.parseColor("#F28225")
        style = Paint.Style.STROKE
        strokeWidth = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 1f, resources.displayMetrics)
        strokeJoin = Paint.Join.ROUND
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }

    /**
     * 绘制评估结果
     */
    fun drawResult(points: List<GazePoint>){
        Logger.d(TAG, msg = "开始绘制扫视能力评估结果，原始点数量: ${points.size}")

        val list = points.filter { it.checkValid() }
        Logger.d(TAG, msg = "过滤后有效点数量: ${list.size}")

        gazePoints.clear()
        gazePoints.addAll(list)
        gazePath.reset()

        gazePoints.forEachIndexed { index, result ->
            val x = result.x!! * screenWidth
            val y = result.y!! * screenHeight
            Logger.d(TAG, msg = "绘制点[$index]: 原始坐标(${result.x}, ${result.y}) -> 屏幕坐标($x, $y)")

            if (index == 0){
                gazePath.moveTo(x,y)
            }else{
                gazePath.lineTo(x,y)
            }
        }

        Logger.d(TAG, msg = "扫视能力评估结果绘制完成，触发重绘")
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawPath(gazePath, gazePathPaint)
        gazePoints.forEachIndexed { index, result ->
            val circleX = result.x!! * screenWidth
            val circleY = result.y!! * screenHeight
            val serialNumber = (index + 1).toString()
            canvas.drawCircle(circleX, circleY, gazePointRadius.toFloat(), gazePointPaint)
            // 计算文本边界框以确定文本尺寸
            val textBounds = Rect()
            gazeIndexPaint.getTextBounds(serialNumber, 0, serialNumber.length, textBounds)
            canvas.drawText(serialNumber, circleX, circleY - textBounds.exactCenterY(), gazeIndexPaint)

            val durationText = "${result.duration}ms"
            val durationBounds = Rect()
            gazeIndexPaint.getTextBounds(durationText, 0, durationText.length, durationBounds)
            canvas.drawText(durationText, circleX, circleY - gazePointRadius - 5 - textBounds.bottom, gazeDurationPaint)
        }
    }

    /**
     * 获取视点半径
     */
    fun getGazePointRadius(): Int = gazePointRadius

    /**
     * 获取屏幕宽度
     */
    fun getScreenWidth(): Int = screenWidth

    /**
     * 获取屏幕高度
     */
    fun getScreenHeight(): Int = screenHeight

    /**
     * 获取当前视图的bitmap
     */
    fun getViewBitmap(): Bitmap? {
        return try {
            Logger.d(TAG, msg = "开始生成视图bitmap，尺寸: ${width}x${height}")

            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)

            // 绘制白色背景
            canvas.drawColor(Color.WHITE)

            // 绘制视图内容
            draw(canvas)

            Logger.d(TAG, msg = "视图bitmap生成成功")
            bitmap
        } catch (e: Exception) {
            Logger.e(TAG, msg = "生成视图bitmap失败: ${e.message}")
            null
        }
    }

    /**
     * 获取当前视线点数据
     */
    fun getGazePoints(): List<GazePoint> = gazePoints.toList()

    /**
     * 获取绘制配置信息
     */
    fun getDrawingConfig(): Map<String, Any> {
        return mapOf(
            "gazePointRadius" to gazePointRadius,
            "gazePointColor" to "#F28225",
            "pathStrokeWidth" to "1dp",
            "pathColor" to "#F28225",
            "indexTextSize" to "10sp",
            "durationTextSize" to "10sp",
            "screenWidth" to screenWidth,
            "screenHeight" to screenHeight,
            "totalGazePoints" to gazePoints.size
        )
    }
}