package com.mitdd.gazetracker.movement.saccade

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.PointFEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.ImageView
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.mitdd.gazetracker.R

/**
 * FileName: SaccadeAbilityEvaluatingView
 * Author by lilin,Date on 2024/12/12 16:55
 * PS: Not easy to write code, please indicate.
 * 扫视能力评估view
 */
class SaccadeAbilityEvaluatingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr){

    companion object {
        private val TAG = SaccadeAbilityEvaluatingView::class.java.simpleName
    }

    private val screenWidth = ScreenUtil.getScreenWidth(context)
    private val screenHeight = ScreenUtil.getScreenHeight(context)

    private var mRadius = 25.dp2px(context)

    private val targetView = ImageView(context)

    init {
        val params = LayoutParams(mRadius * 2,mRadius * 2)
        targetView.setBackgroundResource(R.drawable.common_black_round_bg)
        addView(targetView,params)
        targetView.isVisible = false

        Logger.d(TAG, msg = "扫视能力评估视图初始化完成")
        Logger.d(TAG, msg = "屏幕尺寸: ${screenWidth}x${screenHeight}")
        Logger.d(TAG, msg = "目标点半径: ${mRadius}px")
        Logger.d(TAG, msg = "目标点尺寸: ${mRadius * 2}x${mRadius * 2}px")
    }

    /**
     * 开始评估
     * @param pointF 评估点
     */
    fun startEvaluating(pointF: PointF){
        Logger.d(TAG, msg = "开始显示扫视目标点")
        Logger.d(TAG, msg = "目标点比例坐标: (${pointF.x}, ${pointF.y})")

        targetView.isVisible = false

        val screenX = pointF.x * screenWidth - mRadius
        val screenY = pointF.y * screenHeight - mRadius

        targetView.translationX = screenX
        targetView.translationY = screenY

        Logger.d(TAG, msg = "目标点屏幕位置: (${screenX}, ${screenY})")
        Logger.d(TAG, msg = "目标点中心坐标: (${screenX + mRadius}, ${screenY + mRadius})")

        targetView.isVisible = true
        Logger.d(TAG, msg = "扫视目标点显示完成")
    }

    /**
     * 获取目标点配置信息
     */
    fun getTargetConfig(): Map<String, Any> {
        return mapOf(
            "radius" to mRadius,
            "diameter" to (mRadius * 2),
            "screenWidth" to screenWidth,
            "screenHeight" to screenHeight,
            "backgroundColor" to "common_black_round_bg",
            "isVisible" to targetView.isVisible
        )
    }

}