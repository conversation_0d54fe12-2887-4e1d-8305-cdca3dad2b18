package com.mitdd.gazetracker.movement.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.repository.SaccadeAbilityRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * FileName: SaccadeAbilityViewModel
 * Author by lilin,Date on 2025/6/21 10:30
 * PS: Not easy to write code, please indicate.
 * 扫视能力检测ViewModel
 */
class SaccadeAbilityViewModel : ViewModel() {

    companion object {
        private val TAG = SaccadeAbilityViewModel::class.java.simpleName
    }

    private val saccadeAbilityRepository = SaccadeAbilityRepository()
    private val gson = Gson()

    /**
     * 提交结果LiveData
     */
    val submitResultLiveData = MutableLiveData<SaccadeAbilityAdd?>()

    /**
     * 图片上传结果LiveData
     */
    val uploadImageResultLiveData = MutableLiveData<FileUploadResponse?>()

    /**
     * 上传图片
     * @param imageFile 图片文件
     */
    fun uploadImage(imageFile: File) {
        Logger.d(TAG, msg = "开始上传扫视能力检测结果图片: ${imageFile.absolutePath}")
        
        viewModelScope.launch {
            try {
                val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
                val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)
                
                MutableStateFlow(saccadeAbilityRepository.uploadImage(body)).collectResponse {
                    onSuccess = { data, _, _ ->
                        Logger.d(TAG, msg = "图片上传成功: ${data?.url}")
                        uploadImageResultLiveData.postValue(data)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "图片上传失败: 返回数据为空")
                        uploadImageResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "图片上传失败: errorCode=$errorCode, errorMsg=$errorMsg")
                        uploadImageResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "图片上传异常: ${e.message}")
                uploadImageResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param patientId 患者ID
     * @param targetPoints 目标点序列
     * @param gazePoints 视线轨迹点列表
     * @param duration 测试持续时间(毫秒)
     * @param notes 测试备注
     * @param imageUrl 图片URL
     * @param saccadeAbilityData 扫视能力特有数据
     */
    fun submitSaccadeAbilityResult(
        patientId: Long,
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>,
        duration: Int = 20000,
        notes: String = "扫视能力测试",
        imageUrl: String? = null,
        saccadeAbilityData: HashMap<String, Any>? = null
    ) {
        Logger.d(TAG, msg = "开始提交扫视能力检测结果")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点数量: ${targetPoints.size}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "测试持续时间: ${duration}ms, 备注: $notes")
        Logger.d(TAG, msg = "图片URL: $imageUrl")
        
        viewModelScope.launch {
            try {
                // 构建提交参数
                val params = buildSubmitParams(
                    patientId, targetPoints, gazePoints, duration, notes, imageUrl, saccadeAbilityData
                )
                
                Logger.d(TAG, msg = "提交参数构建完成，开始发送请求")
                
                MutableStateFlow(saccadeAbilityRepository.submitSaccadeAbilityResult(params)).collectResponse {
                    onSuccess = { data, _, _ ->
                        Logger.d(TAG, msg = "扫视能力检测结果提交成功: ${data?.message}")
                        submitResultLiveData.postValue(data)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "扫视能力检测结果提交失败: 返回数据为空")
                        submitResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "扫视能力检测结果提交失败: errorCode=$errorCode, errorMsg=$errorMsg")
                        submitResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "扫视能力检测结果提交异常: ${e.message}")
                submitResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 构建提交参数
     */
    private fun buildSubmitParams(
        patientId: Long,
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>,
        duration: Int,
        notes: String,
        imageUrl: String?,
        saccadeAbilityData: HashMap<String, Any>?
    ): HashMap<String, Any> {
        val params = HashMap<String, Any>()
        
        // 患者ID
        params["patientId"] = patientId
        
        // 测试信息
        val testInfo = HashMap<String, Any>()
        testInfo["testType"] = "SACCADE_ABILITY"
        testInfo["testSequence"] = "03"
        testInfo["testDate"] = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date())
        testInfo["duration"] = duration
        testInfo["calibrationParams"] = "\"标准校准\""
        testInfo["environmentInfo"] = "\"室内光线充足\""
        testInfo["notes"] = notes
        params["testInfo"] = testInfo
        
        // 结果数据
        val resultData = HashMap<String, Any>()
        
        // 视线轨迹
        val gazeTrajectory = gazePoints.mapIndexed { index, point ->
            val gazeData = HashMap<String, Any>()
            gazeData["index"] = index + 1
            gazeData["x"] = point.x ?: 0.0
            gazeData["y"] = point.y ?: 0.0
            gazeData["distance"] = point.distance ?: 0.0
            gazeData["duration"] = point.duration ?: 0
            gazeData["timestamp"] = point.timestamp ?: System.currentTimeMillis()
            gazeData["isValid"] = point.checkValid()
            gazeData["skew"] = false
            gazeData
        }
        resultData["gazeTrajectory"] = gazeTrajectory
        
        // 扫视能力数据
        val defaultSaccadeData = HashMap<String, Any>()
        defaultSaccadeData["targetPoints"] = gson.toJson(targetPoints.map { listOf(it.first, it.second) })
        defaultSaccadeData["saccadeEvents"] = "[]"
        defaultSaccadeData["gazeTrajectoryJson"] = "{}"
        defaultSaccadeData["totalSaccades"] = targetPoints.size
        defaultSaccadeData["successfulSaccades"] = calculateSuccessfulSaccades(targetPoints, gazePoints)
        defaultSaccadeData["accuracyRate"] = calculateAccuracyRate(targetPoints, gazePoints)
        defaultSaccadeData["averageSaccadeTime"] = calculateAverageSaccadeTime(gazePoints)
        defaultSaccadeData["saccadeVelocity"] = calculateSaccadeVelocity(gazePoints)
        defaultSaccadeData["errorDistance"] = calculateErrorDistance(targetPoints, gazePoints)
        defaultSaccadeData["latency"] = calculateLatency(gazePoints)
        defaultSaccadeData["peakVelocity"] = calculatePeakVelocity(gazePoints)
        defaultSaccadeData["undershootRate"] = 15.5
        defaultSaccadeData["overshootRate"] = 12.2
        defaultSaccadeData["fixationStability"] = 88.5
        
        // 如果有自定义数据，则合并
        saccadeAbilityData?.let { customData ->
            defaultSaccadeData.putAll(customData)
        }
        
        resultData["saccadeAbilityData"] = defaultSaccadeData
        params["resultData"] = resultData
        
        Logger.d(TAG, msg = "构建的提交参数: ${gson.toJson(params)}")
        
        return params
    }

    /**
     * 计算成功扫视次数
     */
    private fun calculateSuccessfulSaccades(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Int {
        // 简单实现：假设有效的视线点都是成功的扫视
        return gazePoints.count { it.checkValid() }
    }

    /**
     * 计算准确率
     */
    private fun calculateAccuracyRate(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Double {
        if (targetPoints.isEmpty()) return 0.0
        val successfulSaccades = calculateSuccessfulSaccades(targetPoints, gazePoints)
        return (successfulSaccades.toDouble() / targetPoints.size * 100).let { 
            String.format("%.1f", it).toDouble()
        }
    }

    /**
     * 计算平均扫视时间
     */
    private fun calculateAverageSaccadeTime(gazePoints: List<GazePoint>): Double {
        if (gazePoints.isEmpty()) return 0.0
        val validPoints = gazePoints.filter { it.checkValid() }
        if (validPoints.isEmpty()) return 0.0
        
        val totalDuration = validPoints.sumOf { it.duration ?: 0 }
        return (totalDuration.toDouble() / validPoints.size).let {
            String.format("%.1f", it).toDouble()
        }
    }

    /**
     * 计算扫视速度
     */
    private fun calculateSaccadeVelocity(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 2) return 0.0
        
        var totalVelocity = 0.0
        var count = 0
        
        for (i in 1 until gazePoints.size) {
            val prev = gazePoints[i - 1]
            val curr = gazePoints[i]
            
            if (prev.checkValid() && curr.checkValid()) {
                val distance = sqrt(
                    (curr.x!! - prev.x!!).pow(2) + (curr.y!! - prev.y!!).pow(2)
                )
                val timeDiff = (curr.timestamp ?: 0) - (prev.timestamp ?: 0)
                if (timeDiff > 0) {
                    totalVelocity += distance / (timeDiff / 1000.0) // 转换为秒
                    count++
                }
            }
        }
        
        return if (count > 0) {
            (totalVelocity / count * 100).let { // 转换为度/秒的近似值
                String.format("%.1f", it).toDouble()
            }
        } else 0.0
    }

    /**
     * 计算误差距离
     */
    private fun calculateErrorDistance(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Double {
        // 简化实现：计算视线点的平均偏差
        if (gazePoints.isEmpty()) return 0.0
        
        val validPoints = gazePoints.filter { it.checkValid() }
        if (validPoints.isEmpty()) return 0.0
        
        // 假设误差距离为视线点间距离的平均值
        var totalError = 0.0
        var count = 0
        
        for (i in 1 until validPoints.size) {
            val prev = validPoints[i - 1]
            val curr = validPoints[i]
            val distance = sqrt(
                (curr.x!! - prev.x!!).pow(2) + (curr.y!! - prev.y!!).pow(2)
            )
            totalError += distance * 100 // 转换为像素近似值
            count++
        }
        
        return if (count > 0) {
            (totalError / count).let {
                String.format("%.1f", it).toDouble()
            }
        } else 0.0
    }

    /**
     * 计算延迟
     */
    private fun calculateLatency(gazePoints: List<GazePoint>): Double {
        // 简化实现：返回平均反应时间
        if (gazePoints.isEmpty()) return 0.0
        
        val validPoints = gazePoints.filter { it.checkValid() }
        if (validPoints.isEmpty()) return 0.0
        
        val avgDuration = validPoints.sumOf { it.duration ?: 0 }.toDouble() / validPoints.size
        return (avgDuration * 2).let { // 假设延迟是持续时间的2倍
            String.format("%.1f", it).toDouble()
        }
    }

    /**
     * 计算峰值速度
     */
    private fun calculatePeakVelocity(gazePoints: List<GazePoint>): Double {
        val avgVelocity = calculateSaccadeVelocity(gazePoints)
        return (avgVelocity * 1.4).let { // 假设峰值速度是平均速度的1.4倍
            String.format("%.1f", it).toDouble()
        }
    }
}
