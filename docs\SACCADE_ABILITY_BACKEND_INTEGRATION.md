# 扫视能力检测后端联调说明

## 概述

本文档说明如何进行扫视能力检测模块的后端联调，包括数据流程、API接口、日志查看和调试方法。

## 数据流程

### 1. 测试流程
1. 用户进入扫视能力评估页面
2. 系统显示随机目标点，用户进行扫视
3. 系统记录用户的视线轨迹数据
4. 测试完成后，进入结果页面
5. 系统分析数据并生成结果图片
6. 上传图片到服务器
7. 提交检测结果到后端API

### 2. 数据结构

#### 视线轨迹点 (GazePoint)
```kotlin
data class GazePoint(
    val x: Float?,           // X坐标 (0.0-1.0)
    val y: Float?,           // Y坐标 (0.0-1.0)
    val distance: Float?,    // 距离
    val duration: Int?,      // 持续时间(ms)
    val timestamp: Long?,    // 时间戳
    val isValid: Boolean     // 是否有效
)
```

#### 目标点数据
```kotlin
// 目标点列表 (屏幕比例坐标)
val targetPoints = listOf(
    Pair(0.2f, 0.2f),  // 左上
    Pair(0.8f, 0.2f),  // 右上
    Pair(0.8f, 0.8f),  // 右下
    Pair(0.2f, 0.8f),  // 左下
    Pair(0.5f, 0.5f)   // 中心
)
```

## API接口

### 提交扫视能力检测结果

**接口地址**: `POST /api/movement/saccade-ability/submit`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "patientId": 13,
  "testInfo": {
    "testType": "SACCADE_ABILITY",
    "testSequence": "03",
    "testDate": "2023-12-01T12:00:00",
    "duration": 20000,
    "calibrationParams": "\"标准校准\"", 
    "environmentInfo": "\"室内光线充足\"", 
    "notes": "扫视能力测试"
  },
  "resultData": {
    "gazeTrajectory": [
      {
        "index": 1,
        "x": 0.5, 
        "y": 0.7, 
        "distance": 70.2,
        "duration": 80,
        "timestamp": 1701424800000,
        "isValid": true,
        "skew": false
      }
    ],
    "saccadeAbilityData": {
      "targetPoints": "[[0.2,0.2],[0.8,0.2],[0.8,0.8],[0.2,0.8],[0.5,0.5]]",
      "saccadeEvents": "[]",
      "gazeTrajectoryJson": "{}",
      "totalSaccades": 5,
      "successfulSaccades": 4,
      "accuracyRate": 80.0,
      "averageSaccadeTime": 45.2,
      "saccadeVelocity": 250.5,
      "errorDistance": 12.3,
      "latency": 180.5,
      "peakVelocity": 350.2,
      "undershootRate": 15.5,
      "overshootRate": 12.2,
      "fixationStability": 88.5
    }
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "检测结果提交成功",
  "data": {
    "id": 12345,
    "message": "扫视能力检测结果提交成功"
  },
  "timestamp": 1704067200000
}
```

### 图片上传接口

**接口地址**: `POST /api/files/upload/image`

**请求类型**: `multipart/form-data`

**参数**:
- `file`: 图片文件

## 日志查看

### 关键日志标签
- `SaccadeAbilityEvaluatingFragment`: 评估过程日志
- `SaccadeAbilityEvaluateResultActivity`: 结果页面日志
- `SaccadeAbilityViewModel`: 数据处理和提交日志
- `SaccadeAbilityRepository`: 网络请求日志
- `SaccadeAbilityEvaluateResultView`: 绘图相关日志
- `SaccadeAbilityEvaluatingView`: 目标点显示日志

### 重要日志内容

#### 1. 目标点生成和显示
```
生成新的扫视目标点: (0.2, 0.2)
目标点屏幕坐标: (216.0, 432.0)
目标点设置完成，等待用户扫视
```

#### 2. 视线轨迹数据
```
接收到扫视能力评估轨迹数据: {"gaze":[...]}
轨迹数据解析成功，视线点数量: 25
轨迹点[0]: x=0.2, y=0.2, duration=80ms, distance=0.0, timestamp=1701424800000
```

#### 3. 绘图配置
```
=== 扫视能力评估绘图配置信息 ===
视点半径: 10px
视点颜色: #F28225
路径线宽: 1dp
路径颜色: #F28225
屏幕宽度: 1080px
屏幕高度: 2160px
================================
```

#### 4. 数据提交
```
开始提交扫视能力检测结果
患者ID: 13, 目标点数量: 5, 轨迹点数量: 25
构建的提交参数: {"patientId":13,"testInfo":{...},"resultData":{...}}
发送扫视能力检测结果HTTP请求
扫视能力检测结果HTTP请求完成
```

## 后端联调步骤

### 1. 环境配置
确保 `UrlConfig.MOVEMENT_DOMAIN` 配置正确：
```kotlin
const val MOVEMENT_DOMAIN = "https://your-backend-domain.com"
```

### 2. 触发联调
在扫视能力结果页面调用：
```kotlin
// 在SaccadeAbilityEvaluateResultActivity中
startBackendIntegration()
```

### 3. 监控日志
使用以下命令过滤相关日志：
```bash
adb logcat | grep -E "(SaccadeAbility|Movement)"
```

### 4. 验证数据
检查以下关键点：
- 视线轨迹数据是否完整
- 目标点序列是否正确
- 计算的指标是否合理
- 图片是否成功上传
- API请求是否成功

## 调试技巧

### 1. 模拟数据
可以在代码中添加模拟数据进行测试：
```kotlin
// 在SaccadeAbilityEvaluateResultActivity中
private fun generateMockData() {
    val mockGazePoints = listOf(
        GazePoint(0.2f, 0.2f, 0.0f, 100, System.currentTimeMillis()),
        GazePoint(0.8f, 0.2f, 0.6f, 120, System.currentTimeMillis() + 1000),
        // ... 更多模拟数据
    )
    gazePoints = mockGazePoints
}
```

### 2. 网络请求调试
在Repository中添加详细的网络请求日志：
```kotlin
Logger.d(TAG, msg = "请求URL: ${UrlConfig.MOVEMENT_DOMAIN}/api/movement/saccade-ability/submit")
Logger.d(TAG, msg = "请求体: $json")
```

### 3. 数据验证
在提交前验证数据的完整性：
```kotlin
private fun validateData(): Boolean {
    if (gazePoints.isEmpty()) {
        Logger.e(TAG, msg = "视线轨迹数据为空")
        return false
    }
    if (targetPoints.isEmpty()) {
        Logger.e(TAG, msg = "目标点数据为空")
        return false
    }
    return true
}
```

## 常见问题

### 1. 数据为空
- 检查视线追踪服务是否正常启动
- 确认测试过程中有生成有效的视线数据

### 2. 网络请求失败
- 检查网络连接
- 验证服务器地址配置
- 查看服务器端日志

### 3. 图片上传失败
- 检查图片文件是否生成成功
- 验证文件大小和格式
- 确认上传接口是否正常

### 4. 数据格式错误
- 对比API文档检查数据结构
- 验证JSON序列化是否正确
- 检查数据类型匹配

## 联系方式

如有问题，请联系开发团队或查看相关技术文档。
